import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { NextSeo } from 'next-seo';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';

import Layout from '@/components/Layout';
import Hero from '@/components/sections/Hero';
import Features from '@/components/sections/Features';
import HowItWorks from '@/components/sections/HowItWorks';
import Testimonials from '@/components/sections/Testimonials';
import Pricing from '@/components/sections/Pricing';
import About from '@/components/sections/About';
import Contact from '@/components/sections/Contact';
import Newsletter from '@/components/sections/Newsletter';

export default function HomePage() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const { data: session } = useSession();
  const [showAuthSuccess, setShowAuthSuccess] = useState(false);

  // Handle auth success state from URL parameters
  useEffect(() => {
    const { auth, role } = router.query;
    if (auth === 'success') {
      setShowAuthSuccess(true);
      // Clear URL parameters after showing success message
      const timer = setTimeout(() => {
        router.replace('/', undefined, { shallow: true });
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [router]);

  const seoTitle = t('meta.title');
  const seoDescription = t('meta.description');
  const seoKeywords = t('meta.keywords');

  return (
    <>
      <NextSeo
        title={seoTitle}
        description={seoDescription}
        canonical={`https://freela-syria.com${router.asPath}`}
        openGraph={{
          type: 'website',
          locale,
          url: `https://freela-syria.com${router.asPath}`,
          title: seoTitle,
          description: seoDescription,
          images: [
            {
              url: 'https://freela-syria.com/images/og-image.jpg',
              width: 1200,
              height: 630,
              alt: seoTitle,
            },
          ],
          site_name: 'Freela Syria',
        }}
        twitter={{
          handle: '@freela_syria',
          site: '@freela_syria',
          cardType: 'summary_large_image',
        }}
        additionalMetaTags={[
          {
            name: 'keywords',
            content: seoKeywords,
          },
          {
            name: 'author',
            content: 'Freela Syria Team',
          },
          {
            name: 'robots',
            content: 'index,follow',
          },
          {
            name: 'googlebot',
            content: 'index,follow',
          },
        ]}
        languageAlternates={[
          {
            hrefLang: 'ar',
            href: 'https://freela-syria.com/ar',
          },
          {
            hrefLang: 'en',
            href: 'https://freela-syria.com/en',
          },
          {
            hrefLang: 'x-default',
            href: 'https://freela-syria.com',
          },
        ]}
      />

      <Layout>
        {/* Auth Success Notification */}
        {showAuthSuccess && (
          <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-full mx-4">
            <div className="bg-green-500/90 backdrop-blur-md text-white p-4 rounded-lg shadow-lg border border-green-400/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-200" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm font-medium">
                      {session?.user?.role === 'CLIENT'
                        ? (isRTL ? 'تم تسجيل الدخول بنجاح!' : 'Successfully signed in!')
                        : (isRTL ? 'تم تسجيل الدخول بنجاح!' : 'Successfully signed in!')
                      }
                    </p>
                    {session?.user?.role === 'CLIENT' && (
                      <p className="text-xs text-green-200 mt-1">
                        {isRTL
                          ? 'قم بتحميل تطبيق الهاتف المحمول للوصول إلى لوحة التحكم الخاصة بك'
                          : 'Download the mobile app to access your dashboard'
                        }
                      </p>
                    )}
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => setShowAuthSuccess(false)}
                  className="flex-shrink-0 text-green-200 hover:text-white transition-colors"
                  aria-label={isRTL ? 'إغلاق الإشعار' : 'Close notification'}
                  title={isRTL ? 'إغلاق الإشعار' : 'Close notification'}
                >
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        <main className={`${isRTL ? 'rtl' : 'ltr'}`}>
          {/* Hero Section */}
          <Hero />

          {/* Features Section */}
          <Features />

          {/* How It Works Section */}
          <HowItWorks />

          {/* Testimonials Section */}
          <Testimonials />

          {/* Pricing Section */}
          <Pricing />

          {/* About Section */}
          <About />

          {/* Newsletter Section */}
          <Newsletter />

          {/* Contact Section */}
          <Contact />
        </main>
      </Layout>
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ar', ['common', 'landing', 'auth'])),
    },
  };
};
