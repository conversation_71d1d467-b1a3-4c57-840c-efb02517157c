import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from './types/supabase';
export declare const supabase: SupabaseClient<Database>;
export declare const supabaseAdmin: SupabaseClient<Database>;
export declare class SupabaseAIChat {
    private sessionId;
    private subscription;
    constructor(sessionId: string);
    sendMessage(content: string, role: 'user' | 'assistant', messageType?: string): Promise<any>;
    subscribeToMessages(callback: (message: any) => void): any;
    unsubscribe(): void;
    getMessages(limit?: number): Promise<any[]>;
    updateSession(updates: any): Promise<any>;
}
export declare class SupabaseUserService {
    static createUser(userData: {
        email: string;
        password: string;
        firstName: string;
        lastName: string;
        role: 'CLIENT' | 'EXPERT' | 'ADMIN';
        language?: string;
    }): Promise<{
        user: import("@supabase/supabase-js").AuthUser | null;
        session: import("@supabase/supabase-js").AuthSession | null;
    }>;
    static signInWithGoogle(): Promise<{
        provider: import("@supabase/supabase-js").Provider;
        url: string;
    }>;
    static getCurrentUserProfile(): Promise<any>;
    static updateUserProfile(userId: string, updates: any): Promise<any>;
}
export declare class SupabaseExpertService {
    static getExpertProfile(userId: string): Promise<any>;
    static updateExpertProfile(userId: string, updates: any): Promise<any>;
    static createService(expertId: string, serviceData: any): Promise<any>;
}
export declare class SupabaseAIService {
    static startConversation(userId: string, sessionType: string, userRole: 'CLIENT' | 'EXPERT'): Promise<any>;
    static getUserSessions(userId: string): Promise<any[]>;
    static createRecommendation(recommendationData: any): Promise<any>;
    static getUserRecommendations(userId: string): Promise<any[]>;
}
export default supabase;
//# sourceMappingURL=supabase.d.ts.map