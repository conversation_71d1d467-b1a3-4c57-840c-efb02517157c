{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../src/config.ts"], "names": [], "mappings": ";AAAA,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;AAChC,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AAEtD,2BAA2B;AAC3B,MAAM,QAAQ,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;AACtD,MAAM,MAAM,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAClD,MAAM,QAAQ,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;AACtD,MAAM,QAAQ,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;AAEtD,MAAM,QAAQ,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;AAEtD,wBAAwB;AACxB,MAAM,SAAS,GAAG;IAChB,EAAE,EAAE;QACF,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,QAAQ;KACjB;IACD,EAAE,EAAE;QACF,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,EAAE;QACR,MAAM,EAAE,EAAE;QACV,MAAM,EAAE,EAAE;KACX;CACF,CAAC;AAEF,qBAAqB;AACrB,MAAM,UAAU,GAAG;IACjB,SAAS;IACT,GAAG,EAAE,IAAI,EAAE,mBAAmB;IAC9B,WAAW,EAAE,IAAI,EAAE,oBAAoB;IACvC,SAAS,EAAE,QAAQ;IACnB,EAAE,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;IAEjF,aAAa,EAAE;QACb,WAAW,EAAE,KAAK,EAAE,+BAA+B;QACnD,eAAe,EAAE,GAAG;QACpB,MAAM,EAAE,CAAC,KAAU,EAAE,MAA0B,EAAE,GAAuB,EAAE,EAAE;YAC1E,IAAI,MAAM,KAAK,WAAW;gBAAE,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;YACvD,IAAI,MAAM,KAAK,WAAW;gBAAE,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC;YACvD,IAAI,MAAM,KAAK,UAAU,EAAE,CAAC;gBAC1B,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE;oBAC7D,KAAK,EAAE,UAAU;oBACjB,QAAQ,EAAE,KAAK;iBAChB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC;YACD,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBACtB,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAC3F,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;KACF;IAED,SAAS,EAAE;QACT,KAAK,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,SAAS,CAAC;QAC/C,kBAAkB,EAAE,iBAAiB;QACrC,MAAM,EAAE,CAAC,cAAc,CAAC;KACzB;IAED,KAAK,EAAE;QACL,WAAW,EAAE,KAAK;QAClB,QAAQ,EAAE,iBAAiB;QAC3B,aAAa,EAAE,EAAE;QACjB,mBAAmB,EAAE,EAAE;QACvB,0BAA0B,EAAE,IAAI;QAChC,0BAA0B,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC;KACxD;IAED,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;CAC9C,CAAC;AAEF,kBAAkB;AAClB,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAE5C,gDAAgD;AAChD,MAAM,cAAc,GAAG,GAAW,EAAE;IAClC,2BAA2B;IAC3B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACvD,IAAI,MAAM,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED,2BAA2B;IAC3B,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE,CAAC;QACrC,MAAM,WAAW,GAAG,SAAS,CAAC,QAAQ,IAAK,SAAiB,CAAC,YAAY,CAAC;QAC1E,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,oBAAoB;IACpB,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,2BAA2B;AAC3B,MAAM,WAAW,GAAG,CAAC,QAAgB,EAAQ,EAAE;IAC7C,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IAE9B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,YAAY,CAAC,OAAO,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAClD,QAAQ,CAAC,eAAe,CAAC,IAAI,GAAG,QAAQ,CAAC;QACzC,QAAQ,CAAC,eAAe,CAAC,GAAG,GAAG,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;IACnE,CAAC;AACH,CAAC,CAAC;AAEF,uBAAuB;AACvB,MAAM,kBAAkB,GAAG,GAAW,EAAE;IACtC,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC;AAC/B,CAAC,CAAC;AAEF,mCAAmC;AACnC,MAAM,KAAK,GAAG,CAAC,QAAiB,EAAW,EAAE;IAC3C,MAAM,IAAI,GAAG,QAAQ,IAAI,kBAAkB,EAAE,CAAC;IAC9C,OAAO,IAAI,KAAK,IAAI,CAAC;AACvB,CAAC,CAAC;AAEF,qBAAqB;AACrB,MAAM,YAAY,GAAG,CAAC,QAAiB,EAAiB,EAAE;IACxD,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;AACzC,CAAC,CAAC;AAEF,qBAAqB;AACrB,MAAM,YAAY,GAAG,CAAC,QAAiB,EAAoB,EAAE;IAC3D,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;AAC5C,CAAC,CAAC;AAEF,mCAAmC;AACnC,MAAM,kBAAkB,GAAG,GAAS,EAAE;IACpC,MAAM,gBAAgB,GAAG,cAAc,EAAE,CAAC;IAC1C,WAAW,CAAC,gBAAgB,CAAC,CAAC;AAChC,CAAC,CAAC;AAEF,6DAA6D;AAC7D,MAAM,CAAC,OAAO,GAAG;IACf,UAAU;IACV,cAAc;IACd,WAAW;IACX,kBAAkB;IAClB,KAAK;IACL,YAAY;IACZ,YAAY;IACZ,kBAAkB;IAClB,OAAO,EAAE,IAAI;CACd,CAAC"}