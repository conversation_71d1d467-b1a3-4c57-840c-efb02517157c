# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3003
NEXTAUTH_SECRET=development-secret-key-for-freela-syria

# Google OAuth Configuration (production credentials)
GOOGLE_CLIENT_ID=901570477030-to96008habtve92rcrkr12bbipjs236i.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Supabase Configuration
SUPABASE_URL=https://bivignfixaqrmdcbsnqh.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpdmlnbmZpeGFxcm1kY2JzbnFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MzY1MDYsImV4cCI6MjA2NTQxMjUwNn0.cMwSd8oFF5CDyXBaaqPL7EVHhF9l32ERd6krX4DAo4E
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpdmlnbmZpeGFxcm1kY2JzbnFoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTgzNjUwNiwiZXhwIjoyMDY1NDEyNTA2fQ.Ue6KVdG7c-iwZWKr4D-BhRzj82yp2b81uikFYXSdvZ8

# Database Configuration (Legacy - for Prisma compatibility)
DATABASE_URL=postgresql://username:password@localhost:5432/freela_syria

# API Configuration
API_BASE_URL=http://localhost:3003/api
