2025-06-13T11:17:25.244014Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-13T11:17:25.244109Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:25.344451Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\daemon"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-06-13T11:17:25.344482Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:25.398582Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-06-13T11:17:26.042948Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:26.042967Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:27.342564Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\landing-page\\.next\\trace")}
2025-06-13T11:17:27.342588Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }}))
2025-06-13T11:17:29.542509Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:29.542528Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:29.642771Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:29.642787Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:29.743218Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:29.743234Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:29.942607Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:29.942623Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:30.042393Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:30.042409Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:30.142309Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:30.142325Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:30.342592Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:30.342606Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:30.543105Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:30.543120Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:30.943366Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:30.943389Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:31.043411Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:31.043435Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:31.143033Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:31.143055Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:31.441555Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:31.441573Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:31.541939Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:31.541956Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:31.642415Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:31.642453Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:31.842248Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:31.842264Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:31.941990Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:31.942006Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:32.142086Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:32.142109Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:32.343053Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:32.343067Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:32.542419Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:32.542435Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:32.642125Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:32.642143Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:32.841676Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:32.841693Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:32.942393Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:32.942412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:33.041880Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:33.041897Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:33.142834Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:33.142849Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:33.242222Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:33.242238Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:33.342405Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:33.342426Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:33.541786Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:33.541804Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:33.642927Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:33.642943Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:33.842573Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:33.842591Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:34.042646Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:34.042663Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:34.142047Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:34.142062Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:34.342390Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:34.342407Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:34.441595Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:34.441611Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:34.642048Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:34.642066Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:34.742794Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:34.742810Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:34.942503Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:34.942522Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:35.042283Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:35.042301Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:35.242825Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:35.242853Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:35.342274Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:35.342290Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:35.442162Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:35.442178Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:35.642298Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:35.642314Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:35.741481Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:35.741498Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:35.941933Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:35.941952Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:36.142072Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:36.142090Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:36.343181Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:36.343229Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:36.443288Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:36.443306Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:36.742576Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:36.742598Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:36.842471Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:36.842487Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:37.042509Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:37.042525Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:37.142534Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:37.142552Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:37.243242Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:37.243260Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:37.341996Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:37.342013Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:37.543100Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:37.543117Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:37.741959Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:37.741973Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:37.942412Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:37.942430Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:38.042377Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:38.042400Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:38.242175Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:38.242192Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:38.341612Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:38.341630Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:38.542054Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:38.542071Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:38.641938Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:38.641955Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:38.742573Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:38.742589Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:38.941853Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:38.941873Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:39.042226Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:39.042242Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:39.241836Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:39.241854Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:39.442212Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:39.442225Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:39.542343Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:39.542357Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:39.642099Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:39.642117Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:39.742792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:39.742810Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:39.841758Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:39.841774Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:40.042169Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:40.042184Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:40.141876Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:40.141891Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:40.542383Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:40.542397Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:40.741545Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:40.741560Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:40.842567Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:40.842584Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:41.041504Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:41.041519Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:41.141505Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:41.141526Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:41.242598Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:41.242612Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:41.342261Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:41.342277Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:41.442582Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:41.442597Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:41.542675Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:41.542690Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:41.641751Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:41.641766Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:42.041624Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:42.041644Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:42.241775Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:42.241795Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:42.342537Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:42.342552Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:42.542340Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:42.542357Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:42.641820Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:42.641836Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:42.842528Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:42.842543Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:43.043028Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:43.043056Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:43.242865Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:43.242880Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:43.342134Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:43.342150Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:43.542020Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:43.542041Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:43.642683Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:43.642700Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:43.742299Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:43.742314Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:43.842374Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:43.842390Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:43.942804Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:43.942822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:44.042185Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:44.042201Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:44.241965Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:44.241984Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:44.341845Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:44.341865Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:44.442437Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:44.442457Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:44.542248Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:44.542263Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:44.641907Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:44.641927Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:44.741990Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:44.742013Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:44.942071Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:44.942089Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:45.142796Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:45.142812Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:45.242978Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:45.243007Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:45.342528Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:45.342543Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:45.442318Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:45.442341Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:45.642293Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:45.642314Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:45.942037Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:45.942062Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:46.042974Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:46.042998Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:46.142330Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:46.142357Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:46.242204Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:46.242232Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:46.442455Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:46.442475Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:46.642780Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:46.642801Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:46.842712Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:46.842733Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:46.942153Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:46.942179Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:47.043233Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:47.043254Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:47.142095Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:47.142119Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:47.342943Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:47.342982Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:47.442318Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:47.442339Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:47.541665Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:47.541682Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:47.742256Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:47.742274Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:47.843020Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:47.843049Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:47.942974Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:47.942994Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:48.042494Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:48.042515Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:48.141795Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:48.141810Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:48.342207Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:48.342224Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:48.542978Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:48.543003Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:48.641436Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:48.641451Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:48.742355Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:48.742371Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:48.842492Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:48.842507Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:48.942230Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:48.942247Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:49.043394Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:49.043416Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:49.242438Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:49.242454Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:49.341723Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:49.341742Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:49.443001Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:49.443022Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:49.541422Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:49.541439Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:49.742914Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:49.742931Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:49.942517Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:49.942533Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:50.043010Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:50.043028Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:50.142481Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:50.142497Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:50.342382Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:50.342398Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:50.443250Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:50.443267Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:50.542467Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:50.542485Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:50.643452Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:50.643471Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:50.841686Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:50.841702Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:50.942084Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:50.942101Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:51.141986Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:51.142004Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:51.242153Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:51.242191Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:51.441745Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:51.441763Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:51.543293Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:51.543310Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:51.741975Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:51.741996Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:51.842193Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:51.842210Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:51.941585Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:51.941602Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:52.143134Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:52.143150Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:52.242826Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:52.242846Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:52.441987Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:52.442003Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:52.542741Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:52.542755Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:52.742224Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:52.742240Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:52.942125Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:52.942140Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:53.142480Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:53.142497Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:53.242422Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:53.242438Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:53.342384Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:53.342400Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:53.441703Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:53.441721Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:53.642537Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:53.642554Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:53.742490Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:53.742511Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:53.942926Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:53.942943Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:54.141558Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:54.141574Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:54.342666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:54.342683Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:54.542420Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:54.542435Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:54.642789Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:54.642807Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:54.742380Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:54.742424Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:54.942415Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:54.942430Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:55.043424Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:55.043440Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:55.142197Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:55.142212Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:55.343055Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:55.343099Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:55.541635Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:55.541650Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:55.643126Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:55.643141Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:55.742261Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:55.742276Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:55.941595Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:55.941611Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:56.142431Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:56.142447Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:56.342631Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:56.342647Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:56.442340Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:56.442357Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:56.641762Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:56.641778Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:56.742143Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:56.742162Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:56.843149Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:56.843172Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:56.942922Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:56.942946Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:57.041993Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:57.042015Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:57.242177Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:57.242192Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:57.341996Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:57.342013Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:57.543101Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:57.543118Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:57.642264Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:57.642280Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:57.842844Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:57.842860Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:58.042763Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:58.042779Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:58.242512Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:58.242529Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:58.343074Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:58.343090Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:58.442220Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:58.442236Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:58.542869Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:58.542884Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:58.742098Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:58.742115Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:58.842197Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:58.842215Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:58.943561Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:58.943579Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:59.042570Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:59.042586Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:59.243079Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:59.243095Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:59.342472Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:59.342488Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:59.542200Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:59.542220Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:59.642437Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:59.642452Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:59.842530Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:59.842545Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:17:59.941530Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:17:59.941548Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:00.041690Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:00.041708Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:00.142707Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:00.142726Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:00.342136Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:00.342153Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:00.442221Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:00.442244Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:00.542147Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:00.542168Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:00.641830Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:00.641849Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:00.742125Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:00.742142Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:00.843066Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:00.843081Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:00.941853Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:00.941872Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:01.042112Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:01.042129Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:01.142111Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:01.142128Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:01.241707Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:01.241723Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:01.442667Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:01.442691Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:01.541957Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:01.541977Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:01.742192Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:01.742221Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:01.943276Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:01.943298Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:02.042869Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:02.042887Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:02.243524Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:02.243544Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:02.341538Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:02.341556Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:02.542384Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:02.542402Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:02.641813Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:02.641829Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:02.842721Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:02.842736Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:02.941615Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:02.941637Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:03.042842Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:03.042857Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:03.142273Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:03.142290Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:03.342422Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:03.342438Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:03.542639Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:03.542654Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:03.742308Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:03.742332Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:03.842106Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:03.842120Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:04.043492Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:04.043510Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:04.242130Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:04.242145Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:04.442317Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:04.442336Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:04.542411Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:04.542432Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:04.642391Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:04.642409Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:04.742376Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:04.742390Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:04.943044Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:04.943063Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:05.142357Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:05.142372Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:05.241673Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:05.241691Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:05.442262Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:05.442278Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:05.541605Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:05.541622Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:05.742843Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:05.742863Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:05.942602Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:05.942619Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:06.042283Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:06.042304Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:06.142542Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:06.142564Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:06.242237Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:06.242257Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:06.441986Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:06.442003Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:06.542028Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:06.542045Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:06.743111Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:06.743128Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:06.841762Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:06.841778Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:06.942449Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:06.942464Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:07.041698Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:07.041713Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:07.242860Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:07.242877Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:07.342493Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:07.342510Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:07.541959Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:07.541975Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:07.742497Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:07.742512Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:07.942280Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:07.942299Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:08.041853Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:08.041871Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:08.141471Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:08.141486Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:08.342515Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:08.342532Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:08.542157Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:08.542181Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:08.742624Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:08.742643Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:08.841942Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:08.841960Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:08.942052Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:08.942069Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:09.142784Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:09.142801Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:09.342565Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:09.342580Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:09.442464Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:09.442480Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:09.641794Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:09.641810Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:09.742501Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:09.742518Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:09.942639Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:09.942661Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:10.142270Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:10.142286Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:10.242659Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:10.242688Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:10.541552Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:10.541570Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:10.742351Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:10.742366Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:10.843859Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:10.843875Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:11.041726Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:11.041742Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:11.142737Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:11.142753Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:11.343179Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:11.343195Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:11.442129Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:11.442145Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:11.543131Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:11.543147Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:11.643140Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:11.643156Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:11.842338Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:11.842354Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:11.942111Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:11.942131Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:12.142238Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:12.142256Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:12.342339Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:12.342358Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:12.441459Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:12.441478Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:12.643332Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:12.643349Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:12.742774Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:12.742788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:12.942357Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:12.942379Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:13.041930Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:13.041947Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:13.242186Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:13.242202Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:13.342198Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:13.342214Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:13.542736Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:13.542751Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:13.743150Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:13.743166Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:13.841924Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:13.841940Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:13.942678Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:13.942702Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:14.042785Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:14.042800Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:14.141394Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:14.141411Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:14.342927Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:14.342942Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:14.441650Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:14.441665Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:14.542430Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:14.542447Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:14.742629Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:14.742647Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:14.841910Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:14.841926Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:15.041726Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:15.041743Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:15.242909Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:15.242924Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:15.443078Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:15.443093Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:15.541720Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:15.541736Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:15.642211Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:15.642229Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:15.842645Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:15.842662Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:15.942332Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:15.942349Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:16.143451Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:16.143469Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:16.242488Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:16.242504Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:16.443288Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:16.443303Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:16.542203Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:16.542222Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:16.742321Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:16.742336Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:16.942654Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:16.942671Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T11:18:17.142548Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T11:18:17.142569Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T19:21:13.549559Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-06-13T19:21:13.549879Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T19:21:13.650637Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie")}
2025-06-13T19:21:13.650692Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T19:21:13.659728Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-06-13T19:21:13.750848Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-13T19:21:13.750892Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T19:22:20.815795Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-06-13T19:22:20.815835Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T19:22:20.915976Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie")}
2025-06-13T19:22:20.916005Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T19:22:20.993233Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-06-13T19:39:38.714794Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-06-13T19:39:38.715018Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T19:39:38.814749Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-13T19:39:38.814774Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T19:39:38.823205Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-06-13T19:39:38.915392Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-13T19:39:38.915416Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:56:42.550326Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-06-13T20:56:42.550402Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:56:42.652459Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo")}
2025-06-13T20:56:42.652520Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:56:42.719062Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-06-13T20:56:42.951530Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-13T20:56:42.951572Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:56:44.851798Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\landing-page\\.next\\trace")}
2025-06-13T20:56:44.851823Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }}))
2025-06-13T20:57:39.583651Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-06-13T20:57:39.583691Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:39.684613Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie")}
2025-06-13T20:57:39.684642Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:39.794607Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-06-13T20:57:39.883625Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-13T20:57:39.883698Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:52.083102Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:52.083127Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:52.184623Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:52.184654Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:52.282798Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:52.282831Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:52.484597Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:52.484676Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:52.684048Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:52.684072Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:52.783075Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:52.783096Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:52.983357Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:52.983391Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:53.084185Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:53.084214Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:53.183582Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:53.183606Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:53.383713Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:53.383741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:53.482964Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:53.482994Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:53.583843Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:53.583876Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:53.684508Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:53.684542Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:53.782592Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:53.782623Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:53.885182Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:53.885218Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:53.988475Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:53.988506Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:54.083704Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:54.083737Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:54.185469Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:54.185496Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:54.383304Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:54.383330Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:54.583209Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:54.583238Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:54.685266Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:54.685309Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:54.884059Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T20:57:54.884086Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:54.983782Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\landing-page\\.next\\_events.json")}
2025-06-13T20:57:54.983819Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }}))
2025-06-13T20:57:55.922071Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13"), AnchoredSystemPathBuf("apps\\landing-page\\.next")}
2025-06-13T20:57:55.922095Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:57:55.922250Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-13T20:57:56.184056Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\types\\dist\\index.js"), AnchoredSystemPathBuf("packages\\types\\dist\\index.js.map")}
2025-06-13T20:57:56.184085Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/types"), path: AnchoredSystemPathBuf("packages\\types") }}))
2025-06-13T20:57:56.687594Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\types\\dist\\client.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\client.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\i18n.js"), AnchoredSystemPathBuf("packages\\types\\dist\\ui.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\user.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\payment.d.ts"), AnchoredSystemPathBuf("packages\\i18n\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\user.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\user.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\ui.js"), AnchoredSystemPathBuf("packages\\types\\dist\\user.js"), AnchoredSystemPathBuf("packages\\i18n\\dist\\config.js"), AnchoredSystemPathBuf("packages\\types\\dist\\ui.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\i18n.js.map"), AnchoredSystemPathBuf("packages\\i18n\\dist\\config.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\admin.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\expert.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\i18n.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\payment.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\expert.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\ui.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\chat.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\api.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\expert.d.ts.map"), AnchoredSystemPathBuf("packages\\i18n\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\service.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\admin.js"), AnchoredSystemPathBuf("packages\\types\\dist\\payment.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\service.js.map"), AnchoredSystemPathBuf("packages\\i18n\\dist\\config.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\chat.js"), AnchoredSystemPathBuf("packages\\i18n\\dist\\index.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\admin.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\api.js"), AnchoredSystemPathBuf("packages\\types\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\chat.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\admin.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\expert.js"), AnchoredSystemPathBuf("packages\\types\\dist\\booking.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\i18n.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\payment.js"), AnchoredSystemPathBuf("packages\\types\\dist\\api.d.ts.map"), AnchoredSystemPathBuf("packages\\i18n\\dist\\index.js"), AnchoredSystemPathBuf("packages\\types\\dist\\api.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\client.js"), AnchoredSystemPathBuf("packages\\types\\dist\\booking.js"), AnchoredSystemPathBuf("packages\\types\\dist\\client.d.ts.map"), AnchoredSystemPathBuf("packages\\i18n\\dist\\config.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\service.js"), AnchoredSystemPathBuf("packages\\types\\dist\\booking.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\service.d.ts"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\_events.json"), AnchoredSystemPathBuf("packages\\types\\dist\\booking.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\chat.d.ts")}
2025-06-13T20:57:56.687629Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }, WorkspacePackage { name: Other("@freela/types"), path: AnchoredSystemPathBuf("packages\\types") }, WorkspacePackage { name: Other("@freela/i18n"), path: AnchoredSystemPathBuf("packages\\i18n") }}))
2025-06-13T20:57:58.570759Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-13T20:57:58.571260Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("packages\\utils\\dist\\auth.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\auth.js"), AnchoredSystemPathBuf("packages\\database\\dist\\supabase.js"), AnchoredSystemPathBuf("packages\\database\\dist\\types\\supabase.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\static\\development\\_ssgManifest.js"), AnchoredSystemPathBuf("packages\\utils\\dist\\validation.js.map"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next"), AnchoredSystemPathBuf("packages\\utils\\dist\\auth.d.ts"), AnchoredSystemPathBuf("packages\\utils\\dist\\validation.d.ts.map"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\static\\development\\_buildManifest.js"), AnchoredSystemPathBuf("packages\\database\\dist"), AnchoredSystemPathBuf("packages\\utils\\dist\\constants.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("packages\\utils\\dist\\auth.d.ts.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\constants.d.ts.map"), AnchoredSystemPathBuf("packages\\database\\dist\\supabase.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\constants.js.map"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("packages\\database\\dist\\client.d.ts"), AnchoredSystemPathBuf("packages\\utils\\dist\\validation.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\build-manifest.json"), AnchoredSystemPathBuf("packages\\database\\dist\\types\\supabase.d.ts"), AnchoredSystemPathBuf("packages\\database\\dist\\types\\supabase.d.ts.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\index.js"), AnchoredSystemPathBuf("packages\\database\\dist\\types"), AnchoredSystemPathBuf("packages\\utils\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\utils\\dist\\constants.d.ts"), AnchoredSystemPathBuf("packages\\database\\dist\\client.d.ts.map"), AnchoredSystemPathBuf("packages\\database\\dist\\client.js"), AnchoredSystemPathBuf("packages\\database\\dist\\types\\supabase.js"), AnchoredSystemPathBuf("packages\\database\\dist\\client.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\index.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\validation.d.ts")}
2025-06-13T20:57:58.571283Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }, WorkspacePackage { name: Other("@freela/utils"), path: AnchoredSystemPathBuf("packages\\utils") }, WorkspacePackage { name: Other("@freela/database"), path: AnchoredSystemPathBuf("packages\\database") }}))
2025-06-13T20:57:58.571501Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-13T20:57:59.068988Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\database\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\database\\dist\\supabase.d.ts.map"), AnchoredSystemPathBuf("packages\\database\\dist\\index.js.map"), AnchoredSystemPathBuf("packages\\database\\dist\\seed.d.ts.map"), AnchoredSystemPathBuf("packages\\database\\dist\\supabase.d.ts"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13"), AnchoredSystemPathBuf("packages\\database\\dist"), AnchoredSystemPathBuf("packages\\database\\dist\\seed.js"), AnchoredSystemPathBuf("packages\\database\\dist\\seed.js.map"), AnchoredSystemPathBuf("packages\\database\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("packages\\database\\dist\\seed.d.ts"), AnchoredSystemPathBuf("packages\\database\\dist\\index.js")}
2025-06-13T20:57:59.069015Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }, WorkspacePackage { name: Other("@freela/database"), path: AnchoredSystemPathBuf("packages\\database") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T20:58:00.387437Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-13T20:58:00.388481Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\types"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\development\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\chunks\\polyfills.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\trace"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\package.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\development"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\development\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\package.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\static\\development\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\development"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\development\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\trace"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\trace"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\development\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\static\\development\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\types"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\chunks\\polyfills.js")}
2025-06-13T20:58:00.388514Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/admin-dashboard"), path: AnchoredSystemPathBuf("apps\\admin-dashboard") }, WorkspacePackage { name: Other("@freela/expert-dashboard"), path: AnchoredSystemPathBuf("apps\\expert-dashboard") }, WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }}))
2025-06-13T20:59:18.179006Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\development\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\images\\18rQ8gzcG2-Q1a6xSozK46kPPSwlqGF-94YZJJ5g2HU="), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\server-development\\index.pack.gz.old"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\images"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\client-development\\1.pack.gz"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\server-development\\index.pack.gz"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\trace"), AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\development"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\client-development\\index.pack.gz.old"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\development\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\server-development\\1.pack.gz"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\types"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\types"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\images\\18rQ8gzcG2-Q1a6xSozK46kPPSwlqGF-94YZJJ5g2HU=\\60.1749847973647.0h44-99BaZw1TQGctrOT06FaHBJrwSXdF+akYEr4km0=.avif"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\server-development\\1.pack.gz_"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\server-development"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\server-development\\3.pack.gz_"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\client-development"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\client-development\\4.pack.gz_"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\server-development\\index.pack.gz_"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\trace"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\development"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\server-development\\2.pack.gz"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\client-development\\1.pack.gz_"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\images\\18rQ8gzcG2-Q1a6xSozK46kPPSwlqGF-94YZJJ5g2HU=\\60.1749848355493.0h44-99BaZw1TQGctrOT06FaHBJrwSXdF+akYEr4km0=.avif"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\chunks\\polyfills.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\chunks\\polyfills.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\trace"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\client-development\\4.pack.gz"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\client-development\\index.pack.gz"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\client-development\\index.pack.gz_"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\server-development\\2.pack.gz_"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\server-development\\4.pack.gz"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\api\\logs\\combined.log"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\pages\\api\\auth\\[...nextauth].js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\development\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\cache\\webpack\\server-development\\4.pack.gz_"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\vendor-chunks\\next.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\development\\_ssgManifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\build-manifest.json")}
2025-06-13T20:59:18.180560Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/api"), path: AnchoredSystemPathBuf("apps\\api") }, WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@freela/expert-dashboard"), path: AnchoredSystemPathBuf("apps\\expert-dashboard") }, WorkspacePackage { name: Other("@freela/admin-dashboard"), path: AnchoredSystemPathBuf("apps\\admin-dashboard") }}))
2025-06-13T20:59:18.180837Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-13T20:59:18.186084Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-13T20:59:18.191332Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-13T21:13:19.247248Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-13T21:13:19.247309Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:19.312409Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies\\1.cookie"), AnchoredSystemPathBuf(".turbo"), AnchoredSystemPathBuf(".turbo\\cookies\\.turbo-cookie"), AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-13T21:13:19.312443Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:19.346340Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-06-13T21:13:19.917059Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\cookies")}
2025-06-13T21:13:19.917086Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:21.909885Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\landing-page\\.next\\trace")}
2025-06-13T21:13:21.909916Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }}))
2025-06-13T21:13:50.210287Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:50.210304Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:50.311317Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:50.311345Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:50.910213Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:50.910233Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:51.010207Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:51.010288Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:51.910558Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:51.910584Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:52.013886Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:52.013916Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:52.110229Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:52.110265Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:52.209943Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:52.210031Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:52.313350Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:52.313373Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:52.512052Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:52.512095Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:52.612215Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:52.612251Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:52.711513Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:52.711548Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:52.812219Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:52.812250Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:52.910245Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:52.910332Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:53.021568Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:53.021718Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:53.111780Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:53.111815Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:53.210267Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:53.210300Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:53.310072Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\landing-page\\.next\\_events.json"), AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13")}
2025-06-13T21:13:53.310159Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:54.240255Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\i18n\\dist\\config.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\index.d.ts.map"), AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13"), AnchoredSystemPathBuf("packages\\types\\dist\\index.js"), AnchoredSystemPathBuf("packages\\types\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\index.js.map"), AnchoredSystemPathBuf("packages\\i18n\\dist\\index.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next"), AnchoredSystemPathBuf("packages\\i18n\\dist\\config.d.ts"), AnchoredSystemPathBuf("packages\\i18n\\dist\\index.js.map"), AnchoredSystemPathBuf("packages\\i18n\\dist\\config.js.map"), AnchoredSystemPathBuf("packages\\i18n\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\i18n\\dist\\config.js"), AnchoredSystemPathBuf("packages\\i18n\\dist\\index.d.ts.map")}
2025-06-13T21:13:54.240286Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/i18n"), path: AnchoredSystemPathBuf("packages\\i18n") }, WorkspacePackage { name: Other("@freela/types"), path: AnchoredSystemPathBuf("packages\\types") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }}))
2025-06-13T21:13:54.662841Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-13T21:13:54.663201Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages\\types\\dist\\user.js"), AnchoredSystemPathBuf("packages\\types\\dist\\client.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\user.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\service.js"), AnchoredSystemPathBuf("packages\\types\\dist\\chat.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\expert.js"), AnchoredSystemPathBuf("packages\\types\\dist\\ui.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\api.js"), AnchoredSystemPathBuf("packages\\types\\dist\\client.js"), AnchoredSystemPathBuf("packages\\types\\dist\\expert.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\api.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\admin.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\expert.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\i18n.js"), AnchoredSystemPathBuf("packages\\types\\dist\\user.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\payment.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\ui.js"), AnchoredSystemPathBuf("packages\\types\\dist\\client.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\api.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\ui.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\admin.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\i18n.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\ui.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\chat.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\i18n.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\booking.js"), AnchoredSystemPathBuf("packages\\types\\dist\\client.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\expert.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\booking.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\payment.js"), AnchoredSystemPathBuf("packages\\types\\dist\\booking.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\user.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\admin.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\chat.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\chat.js"), AnchoredSystemPathBuf("packages\\types\\dist\\api.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\admin.js"), AnchoredSystemPathBuf("packages\\types\\dist\\payment.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\service.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\payment.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\service.d.ts.map"), AnchoredSystemPathBuf("packages\\types\\dist\\service.js.map"), AnchoredSystemPathBuf("packages\\types\\dist\\i18n.d.ts"), AnchoredSystemPathBuf("packages\\types\\dist\\booking.d.ts.map")}
2025-06-13T21:13:54.663224Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/types"), path: AnchoredSystemPathBuf("packages\\types") }}))
2025-06-13T21:13:54.663308Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-13T21:13:54.709729Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\landing-page\\.next\\_events.json")}
2025-06-13T21:13:54.709752Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }}))
2025-06-13T21:13:56.896735Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("packages\\database\\dist\\types\\supabase.js.map"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\chunks"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\static\\development\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\package.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("packages\\utils\\dist\\constants.d.ts"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\chunks"), AnchoredSystemPathBuf("packages\\utils\\dist\\constants.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("packages\\database\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("packages\\database\\dist\\types\\supabase.d.ts"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next"), AnchoredSystemPathBuf("packages\\utils\\dist\\auth.d.ts.map"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\development"), AnchoredSystemPathBuf("packages\\database\\dist\\seed.js"), AnchoredSystemPathBuf(".turbo\\daemon\\fb80d5b2fd9546e7-turbo.log.2025-06-13"), AnchoredSystemPathBuf("packages\\utils\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\utils\\dist\\auth.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\types"), AnchoredSystemPathBuf("packages\\database\\dist\\types\\supabase.d.ts.map"), AnchoredSystemPathBuf("packages\\database\\dist\\client.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\constants.d.ts.map"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\development"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server"), AnchoredSystemPathBuf("packages\\database\\dist\\seed.d.ts"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\chunks\\polyfills.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("packages\\database\\dist\\client.d.ts.map"), AnchoredSystemPathBuf("packages\\database\\dist\\client.d.ts"), AnchoredSystemPathBuf("packages\\database\\dist\\supabase.d.ts"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\static\\development\\_ssgManifest.js"), AnchoredSystemPathBuf("packages\\database\\dist\\index.d.ts"), AnchoredSystemPathBuf("packages\\utils\\dist\\auth.js.map"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\react-loadable-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\app-build-manifest.json"), AnchoredSystemPathBuf("packages\\database\\dist\\supabase.d.ts.map"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\development\\_buildManifest.js"), AnchoredSystemPathBuf("packages\\utils\\dist\\index.js.map"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static\\development\\_ssgManifest.js"), AnchoredSystemPathBuf("packages\\utils\\dist\\auth.d.ts"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\development\\_buildManifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\middleware-build-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\app-paths-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\development\\_ssgManifest.js"), AnchoredSystemPathBuf("packages\\database\\dist\\seed.js.map"), AnchoredSystemPathBuf("packages\\utils\\dist\\validation.d.ts"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("packages\\database\\dist\\index.js.map"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\trace"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\next-font-manifest.json"), AnchoredSystemPathBuf("packages\\utils\\dist\\constants.js.map"), AnchoredSystemPathBuf("packages\\database\\dist\\types\\supabase.js"), AnchoredSystemPathBuf("packages\\utils\\dist\\index.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\next-font-manifest.js"), AnchoredSystemPathBuf("packages\\utils\\dist\\validation.js.map"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\static"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\package.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next"), AnchoredSystemPathBuf("packages\\database\\dist\\supabase.js.map"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\server\\pages-manifest.json"), AnchoredSystemPathBuf("packages\\database\\dist\\seed.d.ts.map"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\build-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next"), AnchoredSystemPathBuf("packages\\database\\dist\\client.js"), AnchoredSystemPathBuf("packages\\utils\\dist\\validation.d.ts.map"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\static\\chunks\\polyfills.js"), AnchoredSystemPathBuf("packages\\database\\dist\\supabase.js"), AnchoredSystemPathBuf("packages\\utils\\dist\\validation.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\types\\package.json"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\trace"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\middleware-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\server-reference-manifest.json"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\server\\middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\types"), AnchoredSystemPathBuf("packages\\utils\\dist\\index.d.ts.map"), AnchoredSystemPathBuf("apps\\expert-dashboard\\.next\\trace"), AnchoredSystemPathBuf("apps\\admin-dashboard\\.next\\server\\server-reference-manifest.js"), AnchoredSystemPathBuf("apps\\landing-page\\.next\\build-manifest.json"), AnchoredSystemPathBuf("packages\\database\\dist\\index.js")}
2025-06-13T21:13:56.896769Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@freela/landing-page"), path: AnchoredSystemPathBuf("apps\\landing-page") }, WorkspacePackage { name: Other("@freela/admin-dashboard"), path: AnchoredSystemPathBuf("apps\\admin-dashboard") }, WorkspacePackage { name: Other("@freela/database"), path: AnchoredSystemPathBuf("packages\\database") }, WorkspacePackage { name: Other("@freela/expert-dashboard"), path: AnchoredSystemPathBuf("apps\\expert-dashboard") }, WorkspacePackage { name: Other("@freela/utils"), path: AnchoredSystemPathBuf("packages\\utils") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-13T21:13:56.896848Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
