declare const i18n: any;
declare const initReactI18next: any;
declare const arCommon: any;
declare const arAuth: any;
declare const arExpert: any;
declare const arClient: any;
declare const enCommon: any;
declare const resources: {
    ar: {
        common: any;
        auth: any;
        expert: any;
        client: any;
    };
    en: {
        common: any;
        auth: {};
        expert: {};
        client: {};
    };
};
declare const i18nConfig: {
    resources: {
        ar: {
            common: any;
            auth: any;
            expert: any;
            client: any;
        };
        en: {
            common: any;
            auth: {};
            expert: {};
            client: {};
        };
    };
    lng: string;
    fallbackLng: string;
    defaultNS: string;
    ns: string[];
    interpolation: {
        escapeValue: boolean;
        formatSeparator: string;
        format: (value: any, format: string | undefined, lng: string | undefined) => any;
    };
    detection: {
        order: string[];
        lookupLocalStorage: string;
        caches: string[];
    };
    react: {
        useSuspense: boolean;
        bindI18n: string;
        bindI18nStore: string;
        transEmptyNodeValue: string;
        transSupportBasicHtmlNodes: boolean;
        transKeepBasicHtmlNodesFor: string[];
    };
    debug: boolean;
};
declare const detectLanguage: () => string;
declare const setLanguage: (language: string) => void;
declare const getCurrentLanguage: () => string;
declare const isRTL: (language?: string) => boolean;
declare const getDirection: (language?: string) => "ltr" | "rtl";
declare const getTextAlign: (language?: string) => "left" | "right";
declare const initializeLanguage: () => void;
//# sourceMappingURL=config.d.ts.map