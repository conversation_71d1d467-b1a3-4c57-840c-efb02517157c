# Google OAuth Troubleshooting Guide

## Current Issue
**Error**: "Access blocked: This app's request is invalid"
**Status**: OAuth flow initiates correctly but fails at Google's authorization screen

## Verified Working Components ✅
- NextAuth configuration is correct
- Environment variables are properly set
- Redirect URIs are configured in Google Cloud Console
- Landing page is running on correct port (3003)
- OAuth flow initiation works (debug logs show proper URL generation)

## Root Cause Analysis
The "Access blocked" error typically indicates issues with Google Cloud Console OAuth consent screen configuration, not the application code.

## Step-by-Step Resolution

### 1. OAuth Consent Screen Configuration
**Navigate to**: Google Cloud Console → APIs & Services → OAuth consent screen

**Required Settings**:
- **User Type**: External (for testing with personal accounts)
- **Application Name**: "Freela Syria" or similar
- **User Support Email**: Your email address
- **Developer Contact Information**: Your email address
- **Application Domain**: `localhost` (for development)
- **Authorized Domains**: Add `localhost` if required

### 2. Scopes Configuration
**Required Scopes**:
- `openid`
- `email` 
- `profile`

**Verification**: Ensure these exact scopes are added to your OAuth consent screen

### 3. Test Users (Critical for External Apps)
**Navigate to**: OAuth consent screen → Test users

**Action Required**: Add your Google account email as a test user
- Click "Add Users"
- Enter your Gmail address
- Save changes

**Important**: External apps in testing mode can only be used by added test users

### 4. App Verification Status
**Check**: OAuth consent screen status
- **Testing**: App should be in "Testing" mode for development
- **Publishing**: Not required for development with test users

### 5. OAuth Client Configuration
**Navigate to**: APIs & Services → Credentials → OAuth 2.0 Client IDs

**Verify**:
- Client ID: `************-to96008habtve92rcrkr12bbipjs236i.apps.googleusercontent.com`
- Authorized redirect URIs include: `http://localhost:3003/api/auth/callback/google`

## Testing Procedure

### Step 1: Verify OAuth Consent Screen
1. Go to Google Cloud Console
2. Navigate to APIs & Services → OAuth consent screen
3. Ensure app is in "Testing" status
4. Verify all required fields are filled

### Step 2: Add Test User
1. In OAuth consent screen, go to "Test users" section
2. Add your Google account email
3. Save changes

### Step 3: Test Authentication
1. Clear browser cookies for localhost:3003
2. Try Google OAuth login again
3. Should now work without "Access blocked" error

## Common Issues & Solutions

### Issue: "App isn't verified"
**Solution**: This is normal for testing. Click "Advanced" → "Go to Freela Syria (unsafe)"

### Issue: "redirect_uri_mismatch"
**Solution**: Verify exact match of redirect URI in Google Cloud Console

### Issue: "invalid_client"
**Solution**: Check Client ID and Secret in environment variables

## Environment Variables Verification
```bash
# Current configuration (verified working)
NEXTAUTH_URL=http://localhost:3003
GOOGLE_CLIENT_ID=************-to96008habtve92rcrkr12bbipjs236i.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
```

## Next Steps After Resolution
1. Test complete OAuth flow
2. Verify user creation in database
3. Test redirect to dashboard
4. Proceed with AI-powered onboarding integration

## Debug Information
- Landing page running on: http://localhost:3003
- OAuth callback URL: http://localhost:3003/api/auth/callback/google
- NextAuth debug mode: Enabled
- OAuth flow initiation: ✅ Working
- Google authorization: ❌ Blocked (consent screen issue)
