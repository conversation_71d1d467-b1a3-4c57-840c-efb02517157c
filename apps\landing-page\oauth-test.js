/**
 * Google OAuth Configuration Test Script
 * Run this to verify OAuth setup before testing in browser
 */

const https = require('https');
const url = require('url');

// Configuration from environment
const CLIENT_ID = '************-to96008habtve92rcrkr12bbipjs236i.apps.googleusercontent.com';
const REDIRECT_URI = 'http://localhost:3003/api/auth/callback/google';

console.log('🔍 Google OAuth Configuration Test\n');

// Test 1: Verify Google OAuth Discovery Document
console.log('1. Testing Google OAuth Discovery Document...');
https.get('https://accounts.google.com/.well-known/openid-configuration', (res) => {
  let data = '';
  res.on('data', chunk => data += chunk);
  res.on('end', () => {
    try {
      const config = JSON.parse(data);
      console.log('   ✅ Google OAuth Discovery: OK');
      console.log(`   📍 Authorization Endpoint: ${config.authorization_endpoint}`);
      console.log(`   📍 Token Endpoint: ${config.token_endpoint}`);
    } catch (error) {
      console.log('   ❌ Google OAuth Discovery: Failed');
      console.log(`   Error: ${error.message}`);
    }
  });
}).on('error', (error) => {
  console.log('   ❌ Google OAuth Discovery: Network Error');
  console.log(`   Error: ${error.message}`);
});

// Test 2: Verify OAuth URL Construction
console.log('\n2. Testing OAuth URL Construction...');
const authUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth');
authUrl.searchParams.set('client_id', CLIENT_ID);
authUrl.searchParams.set('redirect_uri', REDIRECT_URI);
authUrl.searchParams.set('response_type', 'code');
authUrl.searchParams.set('scope', 'openid email profile');
authUrl.searchParams.set('access_type', 'offline');
authUrl.searchParams.set('prompt', 'consent');

console.log('   ✅ OAuth URL Construction: OK');
console.log(`   📍 Client ID: ${CLIENT_ID}`);
console.log(`   📍 Redirect URI: ${REDIRECT_URI}`);
console.log(`   📍 Scopes: openid email profile`);

// Test 3: Environment Variables Check
console.log('\n3. Testing Environment Variables...');
const requiredEnvVars = [
  'NEXTAUTH_URL',
  'NEXTAUTH_SECRET',
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET'
];

let envCheck = true;
requiredEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`   ✅ ${varName}: Set`);
  } else {
    console.log(`   ❌ ${varName}: Missing`);
    envCheck = false;
  }
});

if (envCheck) {
  console.log('   ✅ All environment variables: OK');
} else {
  console.log('   ❌ Some environment variables: Missing');
}

// Test 4: NextAuth Configuration Check
console.log('\n4. Testing NextAuth Configuration...');
try {
  const nextAuthConfig = require('./src/pages/api/auth/[...nextauth].ts');
  console.log('   ✅ NextAuth config file: Found');
} catch (error) {
  console.log('   ❌ NextAuth config file: Error');
  console.log(`   Error: ${error.message}`);
}

console.log('\n📋 Summary:');
console.log('   - OAuth Discovery: Should be ✅');
console.log('   - URL Construction: Should be ✅');
console.log('   - Environment Variables: Should be ✅');
console.log('   - NextAuth Config: Should be ✅');

console.log('\n🔧 If tests pass but OAuth still fails:');
console.log('   1. Check Google Cloud Console OAuth consent screen');
console.log('   2. Add your email as a test user');
console.log('   3. Ensure app is in "Testing" mode');
console.log('   4. Verify redirect URIs match exactly');

console.log('\n🌐 Test OAuth URL:');
console.log(`   ${authUrl.toString()}`);
