#!/usr/bin/env node

/**
 * Google OAuth Setup Verification Script
 * Run this to diagnose OAuth configuration issues
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

console.log('🔍 Google OAuth Setup Verification\n');
console.log('=' .repeat(50));

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const config = {
  clientId: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  nextAuthUrl: process.env.NEXTAUTH_URL,
  nextAuthSecret: process.env.NEXTAUTH_SECRET,
  supabaseUrl: process.env.SUPABASE_URL,
  supabaseAnonKey: process.env.SUPABASE_ANON_KEY
};

// Test 1: Environment Variables
console.log('\n1. 🔧 Environment Variables Check');
console.log('-'.repeat(30));

const requiredVars = [
  { name: 'GOOGLE_CLIENT_ID', value: config.clientId, required: true },
  { name: 'GOOGLE_CLIENT_SECRET', value: config.clientSecret, required: true },
  { name: 'NEXTAUTH_URL', value: config.nextAuthUrl, required: true },
  { name: 'NEXTAUTH_SECRET', value: config.nextAuthSecret, required: true },
  { name: 'SUPABASE_URL', value: config.supabaseUrl, required: false },
  { name: 'SUPABASE_ANON_KEY', value: config.supabaseAnonKey, required: false }
];

let envCheckPassed = true;
requiredVars.forEach(({ name, value, required }) => {
  if (value) {
    console.log(`   ✅ ${name}: Set (${value.substring(0, 20)}...)`);
  } else if (required) {
    console.log(`   ❌ ${name}: Missing (Required)`);
    envCheckPassed = false;
  } else {
    console.log(`   ⚠️  ${name}: Missing (Optional)`);
  }
});

// Test 2: Google OAuth Discovery
console.log('\n2. 🌐 Google OAuth Discovery');
console.log('-'.repeat(30));

const testGoogleDiscovery = () => {
  return new Promise((resolve) => {
    https.get('https://accounts.google.com/.well-known/openid-configuration', (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const discovery = JSON.parse(data);
          console.log('   ✅ Google OAuth Discovery: OK');
          console.log(`   📍 Auth Endpoint: ${discovery.authorization_endpoint}`);
          console.log(`   📍 Token Endpoint: ${discovery.token_endpoint}`);
          resolve(true);
        } catch (error) {
          console.log('   ❌ Google OAuth Discovery: Parse Error');
          resolve(false);
        }
      });
    }).on('error', (error) => {
      console.log('   ❌ Google OAuth Discovery: Network Error');
      console.log(`   Error: ${error.message}`);
      resolve(false);
    });
  });
};

// Test 3: NextAuth Configuration
console.log('\n3. ⚙️  NextAuth Configuration');
console.log('-'.repeat(30));

const testNextAuthConfig = () => {
  const configPath = path.join(__dirname, 'src/pages/api/auth/[...nextauth].ts');
  
  if (fs.existsSync(configPath)) {
    console.log('   ✅ NextAuth config file: Found');
    
    try {
      const configContent = fs.readFileSync(configPath, 'utf8');
      
      // Check for required components
      const checks = [
        { name: 'GoogleProvider import', pattern: /GoogleProvider.*from.*next-auth\/providers\/google/ },
        { name: 'Client ID configuration', pattern: /GOOGLE_CLIENT_ID/ },
        { name: 'Client Secret configuration', pattern: /GOOGLE_CLIENT_SECRET/ },
        { name: 'Callback configuration', pattern: /callbacks/ },
        { name: 'Session strategy', pattern: /strategy.*jwt/ }
      ];
      
      checks.forEach(({ name, pattern }) => {
        if (pattern.test(configContent)) {
          console.log(`   ✅ ${name}: Found`);
        } else {
          console.log(`   ⚠️  ${name}: Not found or incorrect`);
        }
      });
      
    } catch (error) {
      console.log('   ❌ NextAuth config: Read error');
    }
  } else {
    console.log('   ❌ NextAuth config file: Not found');
  }
};

// Test 4: OAuth URL Generation
console.log('\n4. 🔗 OAuth URL Generation');
console.log('-'.repeat(30));

const testOAuthUrl = () => {
  if (!config.clientId || !config.nextAuthUrl) {
    console.log('   ❌ Cannot generate OAuth URL: Missing configuration');
    return;
  }
  
  const redirectUri = `${config.nextAuthUrl}/api/auth/callback/google`;
  const authUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth');
  
  authUrl.searchParams.set('client_id', config.clientId);
  authUrl.searchParams.set('redirect_uri', redirectUri);
  authUrl.searchParams.set('response_type', 'code');
  authUrl.searchParams.set('scope', 'openid email profile');
  authUrl.searchParams.set('access_type', 'offline');
  authUrl.searchParams.set('prompt', 'consent');
  
  console.log('   ✅ OAuth URL generation: OK');
  console.log(`   📍 Client ID: ${config.clientId}`);
  console.log(`   📍 Redirect URI: ${redirectUri}`);
  console.log(`   📍 Scopes: openid email profile`);
  console.log(`\n   🔗 Test URL:\n   ${authUrl.toString()}`);
};

// Test 5: Common Issues Check
console.log('\n5. 🚨 Common Issues Check');
console.log('-'.repeat(30));

const checkCommonIssues = () => {
  const issues = [];
  
  // Check redirect URI format
  if (config.nextAuthUrl && !config.nextAuthUrl.startsWith('http')) {
    issues.push('NEXTAUTH_URL should start with http:// or https://');
  }
  
  // Check client ID format
  if (config.clientId && !config.clientId.includes('.apps.googleusercontent.com')) {
    issues.push('GOOGLE_CLIENT_ID should end with .apps.googleusercontent.com');
  }
  
  // Check for localhost in production
  if (config.nextAuthUrl && config.nextAuthUrl.includes('localhost') && process.env.NODE_ENV === 'production') {
    issues.push('Using localhost in production environment');
  }
  
  if (issues.length === 0) {
    console.log('   ✅ No common issues detected');
  } else {
    issues.forEach(issue => {
      console.log(`   ⚠️  ${issue}`);
    });
  }
};

// Run all tests
const runTests = async () => {
  testNextAuthConfig();
  await testGoogleDiscovery();
  testOAuthUrl();
  checkCommonIssues();
  
  console.log('\n' + '='.repeat(50));
  console.log('📋 TROUBLESHOOTING CHECKLIST');
  console.log('='.repeat(50));
  
  console.log('\n🔧 If OAuth still fails, check Google Cloud Console:');
  console.log('   1. OAuth consent screen is configured');
  console.log('   2. Your email is added as a test user');
  console.log('   3. App is in "Testing" mode');
  console.log('   4. Redirect URIs match exactly');
  
  console.log('\n🌐 Required redirect URIs in Google Cloud Console:');
  console.log(`   - ${config.nextAuthUrl}/api/auth/callback/google`);
  console.log('   - http://localhost:3000/api/auth/callback/google');
  console.log('   - http://localhost:3001/api/auth/callback/google');
  console.log('   - http://localhost:3003/api/auth/callback/google');
  
  console.log('\n🔍 Next steps:');
  console.log('   1. Fix any ❌ issues above');
  console.log('   2. Configure Google Cloud Console OAuth consent screen');
  console.log('   3. Add your email as a test user');
  console.log('   4. Test OAuth flow again');
  
  console.log('\n✨ Once working, proceed to AI onboarding integration!');
};

// Execute tests
runTests().catch(console.error);
