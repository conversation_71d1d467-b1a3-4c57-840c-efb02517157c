import NextAuth, { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma } from '@freela/database';
import { Prisma } from '@prisma/client';

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
  ],
  pages: {
    signIn: '/',
    error: '/auth/error',
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      try {
        if (account?.provider === 'google') {
          // Check if user already exists
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email || '' }
          });

          if (!existingUser) {
            // Create new user with Google OAuth data
            await prisma.user.create({
              data: {
                email: user.email || '',
                firstName: (profile as Record<string, unknown>)?.given_name as string || user.name?.split(' ')[0] || '',
                lastName: (profile as Record<string, unknown>)?.family_name as string || user.name?.split(' ').slice(1).join(' ') || '',
                avatar: user.image ? { url: user.image } as Prisma.JsonObject : Prisma.JsonNull,
                emailVerified: true,
                status: 'ACTIVE',
                role: 'CLIENT', // Default role, can be changed later
                language: 'ar', // Default to Arabic for Syrian market
                passwordHash: '', // OAuth users don't have passwords
              }
            });
          } else {
            // Update existing user with Google data if needed
            await prisma.user.update({
              where: { email: user.email || '' },
              data: {
                avatar: user.image ? { url: user.image } as Prisma.JsonObject : (existingUser.avatar || Prisma.JsonNull),
                emailVerified: existingUser.emailVerified || true,
                lastLoginAt: new Date(),
              }
            });
          }
        }
        return true;
      } catch (error) {
        // console.error('Error during sign in:', error);
        return false;
      }
    },
    async jwt({ token, user }) {
      if (user) {
        // Fetch complete user data from database
        const dbUser = await prisma.user.findUnique({
          where: { email: user.email || '' },
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            status: true,
            avatar: true,
            language: true,
          }
        });

        if (dbUser) {
          token.id = dbUser.id;
          token.role = dbUser.role;
          token.status = dbUser.status;
          token.language = dbUser.language;
          token.firstName = dbUser.firstName;
          token.lastName = dbUser.lastName;
          token.avatar = dbUser.avatar ? JSON.stringify(dbUser.avatar) : undefined;
        }
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.status = token.status as string;
        session.user.language = token.language as string;
        session.user.firstName = token.firstName as string;
        session.user.lastName = token.lastName as string;
        session.user.avatar = token.avatar as string;
      }
      return session;
    },
    async redirect({ url, baseUrl, token }) {
      // Handle role-based redirects after successful authentication
      try {
        // If URL is relative, make it absolute
        if (url.startsWith('/')) return `${baseUrl}${url}`;

        // If URL is from same origin, allow it
        if (new URL(url).origin === baseUrl) return url;

        // Role-based dashboard redirects
        if (token?.role) {
          switch (token.role) {
            case 'ADMIN':
              return 'http://localhost:3001/dashboard';
            case 'EXPERT':
              return 'http://localhost:3002/dashboard';
            case 'CLIENT':
              // Clients use mobile app, redirect to landing page with success message
              return `${baseUrl}/?auth=success&role=client`;
            default:
              return `${baseUrl}/?auth=success`;
          }
        }

        // Default fallback to landing page
        return `${baseUrl}/?auth=success`;
      } catch (error) {
        console.error('Redirect error:', error);
        return `${baseUrl}/?auth=error`;
      }
    }
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  events: {
    async signIn({ user, account: _account }) {
      // console.log(`User ${user.email} signed in with ${account?.provider}`);
      
      // Update last login time
      if (user.email) {
        await prisma.user.update({
          where: { email: user.email },
          data: { lastLoginAt: new Date() }
        });
      }
    },
    async signOut({ token: _token }) {
      // console.log(`User ${token?.email} signed out`);
    }
  },
  debug: process.env.NODE_ENV === 'development',
};

export default NextAuth(authOptions);
